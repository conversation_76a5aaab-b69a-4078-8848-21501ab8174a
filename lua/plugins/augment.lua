return {
  {
    "augmentcode/augment.vim",
    event = "VeryLazy",
    config = function()
      -- 配置工作区文件夹，让 Augment 能够理解你的项目结构
      vim.g.augment_workspace_folders = { vim.fn.getcwd() }
    end,
    -- 可选：添加一些快捷键映射
    keys = {
      {
        "<leader>ac",
        "<cmd>Augment chat<cr>",
        desc = "Open Augment Chat",
      },
      {
        "<leader>as",
        "<cmd>Augment signin<cr>",
        desc = "Sign in to Augment",
      },
      {
        "<leader>ao",
        "<cmd>Augment signout<cr>",
        desc = "Sign out from Augment",
      },
    },
  },
}
